import { <PERSON>du<PERSON> } from '@nestjs/common';
import { BankDataController } from './bank-data.controller';
import { BankDataService } from './bank-data.service';
import { PrismaService } from '@/infrastructure/prisma/prisma.service';
import { PrismaUserRepository } from '@/infrastructure/repositories/prisma-user.repository';
import { PrismaBankDataRepository } from '@/infrastructure/repositories/prisma-bank-data.repository';

@Module({
  controllers: [BankDataController],
  providers: [
    BankDataService,
    PrismaService,
    {
      provide: 'UserRepository',
      useClass: PrismaUserRepository,
    },
    {
      provide: 'BankDataRepository',
      useClass: PrismaBankDataRepository,
    },
  ],
  exports: [BankDataService],
})
export class BankDataModule {}
