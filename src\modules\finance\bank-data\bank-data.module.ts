import { Module, forwardRef } from '@nestjs/common';
import { BankDataController } from './bank-data.controller';
import { PrismaService } from '@/infrastructure/prisma/prisma.service';
import { PrismaUserRepository } from '@/infrastructure/repositories/prisma-user.repository';
import { BankDataService } from './bank-data.service';
import { PrismaBankDataRepository } from '@/infrastructure/repositories/prisma-bank-data.repository';
import { PrismaBankAccountUpsertDataRepository } from '@/infrastructure/repositories/prisma-bank-account-upsert-data';
import { PrismaEmployeeRepository } from '@/infrastructure/repositories/prisma-employee.repository';
import { PrismaSupplierRepository } from '@/infrastructure/repositories/prisma-supplier.repository';
import { PrismaCustomerRepository } from '@/infrastructure/repositories/prisma-customer.repository';
import { EmployeeModule } from '../employee/employee.module';
import { FinanceModule } from '../finance.module';

@Module({
  imports: [EmployeeModule, forwardRef(() => FinanceModule)],
  controllers: [BankDataController],
  providers: [
    BankDataService,
    PrismaService,
    {
      provide: 'UserRepository',
      useClass: PrismaUserRepository,
    },
    {
      provide: 'BankDataRepository',
      useClass: PrismaBankDataRepository,
    },
    {
      provide: 'BankAccountUpsertDataRepository',
      useClass: PrismaBankAccountUpsertDataRepository,
    },
    {
      provide: 'EmployeeRepository',
      useClass: PrismaEmployeeRepository,
    },
    {
      provide: 'SupplierRepository',
      useClass: PrismaSupplierRepository,
    },
    {
      provide: 'CustomerRepository',
      useClass: PrismaCustomerRepository,
    },
  ],
  exports: [BankDataService],
})
export class BankDataModule {}
