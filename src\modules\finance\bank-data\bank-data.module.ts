import { Modu<PERSON> } from '@nestjs/common';
import { BankDataController } from './bank-data.controller';
import { BankDataService } from './bank-data.service';
import { PrismaService } from '@/infrastructure/prisma/prisma.service';
import { PrismaUserRepository } from '@/infrastructure/repositories/prisma-user.repository';
import { PrismaBankDataRepository } from '@/infrastructure/repositories/prisma-bank-data.repository';
import { PrismaBankAccountUpsertDataRepository } from '@/infrastructure/repositories/prisma-bank-account-upsert-data';

@Module({
  controllers: [BankDataController],
  providers: [
    BankDataService,
    PrismaService,
    {
      provide: 'UserRepository',
      useClass: PrismaUserRepository,
    },
    {
      provide: 'BankDataRepository',
      useClass: PrismaBankDataRepository,
    },
    {
      provide: 'BankAccountUpsertDataRepository',
      useClass: PrismaBankAccountUpsertDataRepository,
    },
  ],
  exports: [BankDataService],
})
export class BankDataModule {}
