import { Injectable } from '@nestjs/common';
import { PrismaService } from '@/infrastructure/prisma/prisma.service';
import {
  BankData,
  Prisma,
  EntityType,
  BankAccountType,
  BankPixKeyType,
  BankDataStatus,
} from '@prisma/client';

@Injectable()
export class PrismaBankDataRepository {
  constructor(private readonly prisma: PrismaService) {}

  async findById(entityUuid: string): Promise<BankData | null> {
    const bankData = await this.prisma.bankData.findUnique({
      where: { entityUuid },
    });
    return bankData;
  }

  async create(data: Prisma.BankDataCreateInput): Promise<BankData> {
    return this.prisma.bankData.create({
      data,
    });
  }

  async update(
    entityUuid: string,
    data: Prisma.BankDataUpdateInput,
  ): Promise<BankData> {
    return this.prisma.bankData.update({
      where: { entityUuid },
      data,
    });
  }

  async findAll({
    entityType,
    bankName,
    bankCode,
    accountType,
    agencyNumber,
    agencyDigit,
    accountNumber,
    accountDigit,
    accountHolderName,
    accountHolderDocument,
    pixKey,
    pixKeyType,
    isDigitalBank,
    createdAt,
    updatedAt,
    status,
  }: {
    entityType?: EntityType;
    bankName?: string;
    bankCode?: string;
    accountType?: BankAccountType;
    agencyNumber?: string;
    agencyDigit?: string;
    accountNumber?: string;
    accountDigit?: string;
    accountHolderName?: string;
    accountHolderDocument?: string;
    pixKey?: string;
    pixKeyType?: BankPixKeyType;
    isDigitalBank?: boolean;
    createdAt?: Date;
    updatedAt?: Date;
    status?: Prisma.EnumBankDataStatusNullableFilter | BankDataStatus | null;
  } = {}): Promise<BankData[]> {
    const where: Prisma.BankDataWhereInput = {};

    if (entityType !== undefined) {
      where.entityType = entityType;
    }

    if (bankName !== undefined) {
      where.bankName = {
        contains: bankName,
        mode: 'insensitive',
      };
    }

    if (bankCode !== undefined) {
      where.bankCode = bankCode;
    }

    if (accountType !== undefined) {
      where.accountType = accountType;
    }

    if (agencyNumber !== undefined) {
      where.agencyNumber = agencyNumber;
    }

    if (agencyDigit !== undefined) {
      where.agencyDigit = agencyDigit;
    }

    if (accountNumber !== undefined) {
      where.accountNumber = accountNumber;
    }

    if (accountDigit !== undefined) {
      where.accountDigit = accountDigit;
    }

    if (accountHolderName !== undefined) {
      where.accountHolderName = {
        contains: accountHolderName,
        mode: 'insensitive',
      };
    }

    if (accountHolderDocument !== undefined) {
      where.accountHolderDocument = accountHolderDocument;
    }

    if (pixKey !== undefined) {
      where.pixKey = {
        contains: pixKey,
        mode: 'insensitive',
      };
    }

    if (pixKeyType !== undefined) {
      where.pixKeyType = pixKeyType;
    }

    if (isDigitalBank !== undefined) {
      where.isDigitalBank = isDigitalBank;
    }

    if (createdAt !== undefined) {
      where.createdAt = createdAt;
    }

    if (updatedAt !== undefined) {
      where.updatedAt = updatedAt;
    }

    if (status !== undefined) {
      where.status = status;
    }

    return this.prisma.bankData.findMany({
      where,
      orderBy: { createdAt: 'desc' },
    });
  }
}
