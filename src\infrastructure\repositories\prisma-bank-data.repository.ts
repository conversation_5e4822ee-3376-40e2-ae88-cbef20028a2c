import { Injectable } from '@nestjs/common';
import { PrismaService } from '@/infrastructure/prisma/prisma.service';
import { BankData, Prisma, EntityType } from '@prisma/client';

@Injectable()
export class PrismaBankDataRepository {
  constructor(private readonly prisma: PrismaService) {}

  async findById(entityUuid: string): Promise<BankData | null> {
    const bankData = await this.prisma.bankData.findUnique({
      where: { entityUuid },
    });
    return bankData;
  }

  async create(data: Prisma.BankDataCreateInput): Promise<BankData> {
    return this.prisma.bankData.create({
      data,
    });
  }

  async update(
    entityUuid: string,
    data: Prisma.BankDataUpdateInput,
  ): Promise<BankData> {
    return this.prisma.bankData.update({
      where: { entityUuid },
      data,
    });
  }

  async delete(entityUuid: string): Promise<void> {
    await this.prisma.bankData.delete({
      where: { entityUuid },
    });
  }

  async findAll(): Promise<BankData[]> {
    return this.prisma.bankData.findMany({
      orderBy: { createdAt: 'desc' },
    });
  }

  async findByEntityType(entityType: EntityType): Promise<BankData[]> {
    return this.prisma.bankData.findMany({
      where: {
        entityType,
      },
      orderBy: { createdAt: 'desc' },
    });
  }

  async exists(entityUuid: string): Promise<boolean> {
    const count = await this.prisma.bankData.count({
      where: {
        entityUuid,
      },
    });
    return count > 0;
  }
}
