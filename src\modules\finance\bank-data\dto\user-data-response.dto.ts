import { ApiProperty } from '@nestjs/swagger';

export class UserDataResponseDto {
  @ApiProperty({
    description: 'User ID',
    example: 'aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee',
  })
  id: string;

  @ApiProperty({
    description: 'User preferred username',
    example: 'john.doe',
  })
  preferred_username: string;

  @ApiProperty({
    description: 'User email',
    example: '<EMAIL>',
  })
  email: string;
}
