import { ApiProperty } from '@nestjs/swagger';

export class EmployeeDataDto {
  @ApiProperty({
    description: 'Employee ID',
    example: 123,
  })
  id: number;

  @ApiProperty({
    description: 'Employee name',
    example: '<PERSON>',
  })
  name: string;

  @ApiProperty({
    description: 'Employee document',
    example: '12345678901',
  })
  document: string;

  @ApiProperty({
    description: 'Employee status',
    example: 'ACTIVE',
  })
  status: string;

  @ApiProperty({
    description: 'Employee creation date',
    example: '2023-01-01T00:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Employee last update date',
    example: '2023-01-01T00:00:00.000Z',
  })
  updatedAt: Date;
}

export class SupplierDataDto {
  @ApiProperty({
    description: 'Supplier ID',
    example: 123,
  })
  id: number;

  @ApiProperty({
    description: 'Supplier name',
    example: 'ABC Company',
  })
  name: string;

  @ApiProperty({
    description: 'Supplier document',
    example: '12345678000195',
  })
  document: string;

  @ApiProperty({
    description: 'Supplier status',
    example: 'ACTIVE',
  })
  status: string;

  @ApiProperty({
    description: 'Supplier creation date',
    example: '2023-01-01T00:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Supplier last update date',
    example: '2023-01-01T00:00:00.000Z',
  })
  updatedAt: Date;
}

export class CustomerDataDto {
  @ApiProperty({
    description: 'Customer ID',
    example: 123,
  })
  id: number;

  @ApiProperty({
    description: 'Customer name',
    example: 'XYZ Corporation',
  })
  name: string;

  @ApiProperty({
    description: 'Customer document',
    example: '98765432000123',
  })
  document: string;

  @ApiProperty({
    description: 'Customer status',
    example: 'ACTIVE',
  })
  status: string;

  @ApiProperty({
    description: 'Customer creation date',
    example: '2023-01-01T00:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Customer last update date',
    example: '2023-01-01T00:00:00.000Z',
  })
  updatedAt: Date;
}

export class UserDataResponseDto {
  @ApiProperty({
    description: 'User ID',
    example: 'aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee',
  })
  id: string;

  @ApiProperty({
    description: 'User email',
    example: '<EMAIL>',
  })
  email: string;

  @ApiProperty({
    description: 'User name',
    example: 'John Doe',
  })
  name: string;

  @ApiProperty({
    description: 'User role',
    example: 'ADMIN',
  })
  role: string;

  @ApiProperty({
    description: 'User creation date',
    example: '2023-01-01T00:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'User last update date',
    example: '2023-01-01T00:00:00.000Z',
  })
  updatedAt: Date;

  @ApiProperty({
    description: 'Employee data (optional)',
    type: EmployeeDataDto,
    required: false,
  })
  employee?: EmployeeDataDto;

  @ApiProperty({
    description: 'Supplier data (optional)',
    type: SupplierDataDto,
    required: false,
  })
  supplier?: SupplierDataDto;

  @ApiProperty({
    description: 'Customer data (optional)',
    type: CustomerDataDto,
    required: false,
  })
  customer?: CustomerDataDto;
}
