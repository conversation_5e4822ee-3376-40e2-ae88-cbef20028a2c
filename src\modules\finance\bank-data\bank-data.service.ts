import { Injectable, BadRequestException } from '@nestjs/common';
import { PrismaService } from '@/infrastructure/prisma/prisma.service';
import { CreateBankDataDto } from './dto/create-bank-data.dto';
import { BankDataResponseDto } from './dto/bank-data-response.dto';
import { BANK_LIST } from './constants/bank-list.constant';
import { BankData, BankDataStatus } from '@prisma/client';

@Injectable()
export class BankDataService {
  constructor(private readonly prisma: PrismaService) {}

  async AdmCreate({
    adminId,
    employeeId,
    createBankDataDto,
  }: {
    adminId: string;
    employeeId: string;
    createBankDataDto: CreateBankDataDto;
  }): Promise<BankDataResponseDto> {
    const safeParseEmployeeId = Number(employeeId.trim());

    if (isNaN(safeParseEmployeeId)) {
      throw new BadRequestException('Invalid employee id');
    }

    // Verificar se o funcionário existe
    const employee = await this.prisma.employee.findUnique({
      where: { id: safeParseEmployeeId },
      include: {
        bankData: true,
      },
    });

    if (!employee) {
      throw new BadRequestException('Employee not found');
    }

    // Verificar se o funcionário já possui dados bancários
    const existingBankData = employee.bankData;

    if (existingBankData) {
      throw new BadRequestException(
        'Employee already has bank data. Please update the existing data instead.',
      );
    }

    // Validar e ajustar o nome do banco baseado no código
    if (BANK_LIST[createBankDataDto.bankCode]) {
      createBankDataDto.bankName = BANK_LIST[createBankDataDto.bankCode];
    }

    const bankData = await this.prisma.bankData.create({
      data: {
        bankName: createBankDataDto.bankName,
        bankCode: createBankDataDto.bankCode,
        accountType: createBankDataDto.accountType,
        agencyNumber: createBankDataDto.agencyNumber,
        agencyDigit: createBankDataDto.agencyDigit,
        accountNumber: createBankDataDto.accountNumber,
        accountDigit: createBankDataDto.accountDigit,
        accountHolderName: createBankDataDto.accountHolderName,
        accountHolderDocument: createBankDataDto.accountHolderDocument,
        pixKey: createBankDataDto.pixKey,
        pixKeyType: createBankDataDto.pixKeyType,
        isDigitalBank: createBankDataDto.isDigitalBank || false,
        status: BankDataStatus.ACTIVE,
        employeeId: employee.id,
      },
    });

    await this.prisma.bankAccountUpsertData.create({
      data: {
        bankDataId: bankData.id,
        fieldName: 'CREATED',
        upsertByUserId: adminId,
      },
    });

    return this.toResponseDto({ employeeId: employee.id, bankData });
  }

  private toResponseDto({
    employeeId,
    bankData,
  }: {
    employeeId: number;
    bankData: BankData;
  }): BankDataResponseDto {
    return {
      id: bankData.id,
      bankName: bankData.bankName,
      bankCode: bankData.bankCode,
      accountType: bankData.accountType,
      agencyNumber: bankData.agencyNumber,
      agencyDigit: bankData.agencyDigit,
      accountNumber: bankData.accountNumber,
      accountDigit: bankData.accountDigit,
      accountHolderName: bankData.accountHolderName,
      accountHolderDocument: bankData.accountHolderDocument,
      pixKey: bankData.pixKey,
      pixKeyType: bankData.pixKeyType,
      isDigitalBank: bankData.isDigitalBank,
      status: bankData.status,
      employeeId,
      createdAt: bankData.createdAt,
      updatedAt: bankData.updatedAt,
    };
  }
}
