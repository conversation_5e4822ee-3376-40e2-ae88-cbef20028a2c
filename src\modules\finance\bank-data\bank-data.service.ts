import {
  Injectable,
  Inject,
  NotFoundException,
  Logger,
  ConflictException,
  ForbiddenException,
} from '@nestjs/common';
import { CreateBankDataDto } from './dto/create-bank-data.dto';
import { BankDataResponseDto } from './dto/bank-data-response.dto';

import { PrismaUserRepository } from '@/infrastructure/repositories/prisma-user.repository';
import { PrismaBankDataRepository } from '@/infrastructure/repositories/prisma-bank-data.repository';
import {PrismaEmployeeRepository} from '@/infrastructure/repositories/prisma-employee.repository';
import { PrismaSupplierRepository } from '@/infrastructure/repositories/prisma-supplier.repository';
import { PrismaCustomerRepository } from '@/infrastructure/repositories/prisma-customer.repository';

import { Role } from '@/core/domain/role.enum';
import { PrismaBankAccountUpsertDataRepository } from '@/infrastructure/repositories/prisma-bank-account-upsert-data';
import { BankData, User } from '@prisma/client';
@Injectable()
export class BankDataService {
  private readonly logger = new Logger(BankDataService.name);
  constructor(
    // private userRepository: UserRepository,
    @Inject('UserRepository')
    private readonly PrismaUserRepository: PrismaUserRepository,
    @Inject('BankDataRepository')
    private readonly PrismaBankDataRepository: PrismaBankDataRepository,
    @Inject('BankAccountUpsertDataRepository')
    private readonly PrismaBankAccountUpsertDataRepository: PrismaBankAccountUpsertDataRepository,
    @Inject('EmployeeRepository')
    private readonly PrismaEmployeeRepository: PrismaEmployeeRepository,
    @Inject('SupplierRepository')
    private readonly PrismaSupplierRepository: PrismaSupplierRepository,
    @Inject('CustomerRepository')
    private readonly PrismaCustomerRepository: PrismaCustomerRepository,
  ) {}

  async create({
    requestUserEmail,
    paramId,
    createBankDataDto,
  }: {
    requestUserEmail: string;
    paramId: string;
    createBankDataDto: CreateBankDataDto;
  }): Promise<BankDataResponseDto> {
    this.logger.log('requestUserEmail', requestUserEmail);
    this.logger.log('paramId', paramId);

    const requestUser =
      await this.PrismaUserRepository.findByEmail(requestUserEmail);
    this.logger.log('requestUser', requestUser);
    if (!requestUser) {
      throw new NotFoundException('Request user not found');
    }

    const userParam = await this.PrismaUserRepository.findById(paramId);
    this.logger.log('userFromParam', userParam);

    if (!userParam) {
      throw new NotFoundException('User not found');
    }

    const verifyBankDataExist =
      await this.PrismaBankDataRepository.findByEntityUuid(userParam.id);

    if (verifyBankDataExist) {
      throw new ConflictException('User already has bank data');
    }

    if (
      requestUser.id !== userParam.id &&
      requestUser.role !== Role.ADMIN &&
      requestUser.role !== Role.FINANCE_ADMIN
    ) {
      throw new ForbiddenException(
        'You do not have permission to perform this action',
      );
    }

    const collaborateRoles = [
      Role.ADMIN,
      Role.FINANCE_ADMIN,
      Role.FINANCE_USER,
      Role.EMPLOYEE,
    ]

    const clientRoles = [
      Role.CUSTOMER,
      Role.CUSTOMER_VIEWER,
    ];

    const supplierRoles = [
      Role.SUPPLIER,
      Role.SUPPLIER_VIEWER,
    ];



    const entityType = collaborateRoles.includes(userParam.role) ? 'COLLABORATE' : clientRoles.includes(userParam.role) ? 'CLIENT' : supplierRoles.includes(userParam.role) ? 'SUPPLIER' : null;     

    if (!entityType) {
      this.logger.error(
        `Role not found for bank data creation: ${userParam.role}`,
      );

      throw new ForbiddenException(
        'User role does not allow bank data creation',
      );
    }

    const bankData = await this.PrismaBankDataRepository.create({
      ...createBankDataDto,
      entityUuid: userParam.id,
      entityType: entityType,
    });

    await this.PrismaBankAccountUpsertDataRepository.create({
      fieldName: 'CREATED',
      entityUuid: requestUser.id,
      entityType: 'COLLABORATE',
      bankData: {
        connect: { id: bankData.id },
      },
    });

    if (userParam.role === Role.EMPLOYEE) {
      const employee = await this.PrismaEmployeeRepository.findByUserId(userParam.id);
      userParam.employee = employee;
    }

    if (supplierRoles.includes(userParam.role)) {
      const supplier = await this.PrismaSupplierRepository.findByUserId(userParam.id);
      userParam.supplier = supplier;
    }

    if (clientRoles.includes(userParam.role)) {
      const customer = await this.PrismaCustomerRepository.findByUserId(userParam.id);
      userParam.customer = customer;
    }

    return this.toResponseDto({
      user: userParam,
      bankData,
    }) as unknown as BankDataResponseDto;

    return {} as BankDataResponseDto; // Placeholder for actual implementation

    // const userWithBankData = await this.prisma.bankData.findMany({
    //   where: { entityUuid: userId },
    // });

    // if (userWithBankData.length > 0) {
    //   throw new BadRequestException(
    //     'User already has bank data. Please update the existing data instead.',
    //   );
    // }

    // // Validar e ajustar o nome do banco baseado no código
    // if (BANK_LIST[createBankDataDto.bankCode]) {
    //   createBankDataDto.bankName = BANK_LIST[createBankDataDto.bankCode];
    // }

    // const bankData = await this.prisma.bankData.create({
    //   data: {
    //     bankName: createBankDataDto.bankName,
    //     bankCode: createBankDataDto.bankCode,
    //     accountType: createBankDataDto.accountType,
    //     agencyNumber: createBankDataDto.agencyNumber,
    //     agencyDigit: createBankDataDto.agencyDigit,
    //     accountNumber: createBankDataDto.accountNumber,
    //     accountDigit: createBankDataDto.accountDigit,
    //     accountHolderName: createBankDataDto.accountHolderName,
    //     accountHolderDocument: createBankDataDto.accountHolderDocument,
    //     pixKey: createBankDataDto.pixKey,
    //     pixKeyType: createBankDataDto.pixKeyType,
    //     isDigitalBank: createBankDataDto.isDigitalBank || false,
    //     status: BankDataStatus.ACTIVE,
    //     entityUuid: userId,
    //     entityType: 'COLLABORATE',
    //   },
    // });

    // await this.prisma.bankAccountUpsertData.create({
    //   data: {
    //     bankDataId: bankData.id,
    //     fieldName: 'CREATED',
    //     entityUuid: requestUserId,
    //     entityType: 'COLLABORATE',
    //   },
    // });
  }

  // async AdmUpdate({
  //   adminId,
  //   employeeId,
  //   updateBankDataDto,
  // }: {
  //   adminId: string;
  //   employeeId: string;
  //   updateBankDataDto: UpdateBankDataDto;
  // }): Promise<BankDataResponseDto> {
  //   const safeParseEmployeeId = Number(employeeId.trim());

  //   if (isNaN(safeParseEmployeeId)) {
  //     throw new BadRequestException('Invalid employee id');
  //   }

  //   const employee = await this.prisma.employee.findUnique({
  //     where: { id: safeParseEmployeeId },
  //   });

  //   if (!employee) {
  //     throw new NotFoundException('Employee not found');
  //   }

  //   const updateData: Partial<BankData> = {};
  //   const changedFields: Array<{
  //     fieldName: string;
  //     oldValue: ChangeLogValue;
  //     newValue: ChangeLogValue;
  //   }> = [];

  //   // Verificar cada campo e adicionar apenas se foi enviado
  //   if (updateBankDataDto.bankName !== undefined) {
  //     if (updateBankDataDto.bankName !== employee.bankData.bankName) {
  //       changedFields.push({
  //         fieldName: 'bankName',
  //         oldValue: employee.bankData.bankName,
  //         newValue: updateBankDataDto.bankName,
  //       });
  //       updateData.bankName = updateBankDataDto.bankName;
  //     }
  //   }

  //   if (updateBankDataDto.bankCode !== undefined) {
  //     // Validar e ajustar o nome do banco baseado no código se fornecido
  //     if (BANK_LIST[updateBankDataDto.bankCode]) {
  //       updateData.bankName = BANK_LIST[updateBankDataDto.bankCode];
  //       if (updateData.bankName !== employee.bankData.bankName) {
  //         changedFields.push({
  //           fieldName: 'bankName',
  //           oldValue: employee.bankData.bankName,
  //           newValue: updateData.bankName,
  //         });
  //       }
  //     }

  //     if (updateBankDataDto.bankCode !== employee.bankData.bankCode) {
  //       changedFields.push({
  //         fieldName: 'bankCode',
  //         oldValue: employee.bankData.bankCode,
  //         newValue: updateBankDataDto.bankCode,
  //       });
  //       updateData.bankCode = updateBankDataDto.bankCode;
  //     }
  //   }

  //   if (updateBankDataDto.accountType !== undefined) {
  //     if (updateBankDataDto.accountType !== employee.bankData.accountType) {
  //       changedFields.push({
  //         fieldName: 'accountType',
  //         oldValue: employee.bankData.accountType,
  //         newValue: updateBankDataDto.accountType,
  //       });
  //       updateData.accountType = updateBankDataDto.accountType;
  //     }
  //   }

  //   if (updateBankDataDto.agencyNumber !== undefined) {
  //     if (updateBankDataDto.agencyNumber !== employee.bankData.agencyNumber) {
  //       changedFields.push({
  //         fieldName: 'agencyNumber',
  //         oldValue: employee.bankData.agencyNumber,
  //         newValue: updateBankDataDto.agencyNumber,
  //       });
  //       updateData.agencyNumber = updateBankDataDto.agencyNumber;
  //     }
  //   }

  //   // Para campos nullable, verificar se foi explicitamente enviado
  //   if (
  //     Object.prototype.hasOwnProperty.call(updateBankDataDto, 'agencyDigit')
  //   ) {
  //     if (updateBankDataDto.agencyDigit !== employee.bankData.agencyDigit) {
  //       changedFields.push({
  //         fieldName: 'agencyDigit',
  //         oldValue: employee.bankData.agencyDigit,
  //         newValue: updateBankDataDto.agencyDigit || null,
  //       });
  //       updateData.agencyDigit = updateBankDataDto.agencyDigit;
  //     }
  //   }

  //   if (updateBankDataDto.accountNumber !== undefined) {
  //     if (updateBankDataDto.accountNumber !== employee.bankData.accountNumber) {
  //       changedFields.push({
  //         fieldName: 'accountNumber',
  //         oldValue: employee.bankData.accountNumber,
  //         newValue: updateBankDataDto.accountNumber,
  //       });
  //       updateData.accountNumber = updateBankDataDto.accountNumber;
  //     }
  //   }

  //   if (updateBankDataDto.accountDigit !== undefined) {
  //     if (updateBankDataDto.accountDigit !== employee.bankData.accountDigit) {
  //       changedFields.push({
  //         fieldName: 'accountDigit',
  //         oldValue: employee.bankData.accountDigit,
  //         newValue: updateBankDataDto.accountDigit,
  //       });
  //       updateData.accountDigit = updateBankDataDto.accountDigit;
  //     }
  //   }

  //   if (updateBankDataDto.accountHolderName !== undefined) {
  //     if (
  //       updateBankDataDto.accountHolderName !==
  //       employee.bankData.accountHolderName
  //     ) {
  //       changedFields.push({
  //         fieldName: 'accountHolderName',
  //         oldValue: employee.bankData.accountHolderName,
  //         newValue: updateBankDataDto.accountHolderName,
  //       });
  //       updateData.accountHolderName = updateBankDataDto.accountHolderName;
  //     }
  //   }

  //   if (updateBankDataDto.accountHolderDocument !== undefined) {
  //     if (
  //       updateBankDataDto.accountHolderDocument !==
  //       employee.bankData.accountHolderDocument
  //     ) {
  //       changedFields.push({
  //         fieldName: 'accountHolderDocument',
  //         oldValue: employee.bankData.accountHolderDocument,
  //         newValue: updateBankDataDto.accountHolderDocument,
  //       });
  //       updateData.accountHolderDocument =
  //         updateBankDataDto.accountHolderDocument;
  //     }
  //   }

  //   // Para campos nullable
  //   if (Object.prototype.hasOwnProperty.call(updateBankDataDto, 'pixKey')) {
  //     if (updateBankDataDto.pixKey !== employee.bankData.pixKey) {
  //       changedFields.push({
  //         fieldName: 'pixKey',
  //         oldValue: employee.bankData.pixKey,
  //         newValue: updateBankDataDto.pixKey || null,
  //       });
  //       updateData.pixKey = updateBankDataDto.pixKey;
  //     }
  //   }

  //   if (Object.prototype.hasOwnProperty.call(updateBankDataDto, 'pixKeyType')) {
  //     if (updateBankDataDto.pixKeyType !== employee.bankData.pixKeyType) {
  //       changedFields.push({
  //         fieldName: 'pixKeyType',
  //         oldValue: employee.bankData.pixKeyType,
  //         newValue: updateBankDataDto.pixKeyType || null,
  //       });
  //       updateData.pixKeyType = updateBankDataDto.pixKeyType;
  //     }
  //   }

  //   if (
  //     Object.prototype.hasOwnProperty.call(updateBankDataDto, 'isDigitalBank')
  //   ) {
  //     if (updateBankDataDto.isDigitalBank !== employee.bankData.isDigitalBank) {
  //       changedFields.push({
  //         fieldName: 'isDigitalBank',
  //         oldValue: employee.bankData.isDigitalBank,
  //         newValue: updateBankDataDto.isDigitalBank || null,
  //       });
  //       updateData.isDigitalBank = updateBankDataDto.isDigitalBank;
  //     }
  //   }

  //   if (Object.prototype.hasOwnProperty.call(updateBankDataDto, 'status')) {
  //     if (updateBankDataDto.status !== employee.bankData.status) {
  //       changedFields.push({
  //         fieldName: 'status',
  //         oldValue: employee.bankData.status,
  //         newValue: updateBankDataDto.status || null,
  //       });
  //       updateData.status = updateBankDataDto.status;
  //     }
  //   }

  //   // Se não há mudanças, retornar os dados atuais
  //   if (changedFields.length === 0) {
  //     return this.toResponseDto({
  //       employeeId: employee.id,
  //       bankData: employee.bankData,
  //     });
  //   }

  //   // Atualizar os dados bancários
  //   const updatedBankData = await this.prisma.bankData.update({
  //     where: { id: employee.bankData.id },
  //     data: updateData,
  //   });

  //   // Registrar as mudanças no histórico
  //   for (const change of changedFields) {
  //     await this.prisma.bankAccountUpsertData.create({
  //       data: {
  //         bankDataId: employee.bankData.id,
  //         fieldName: change.fieldName,
  //         oldValue: this.convertToString(change.oldValue),
  //         newValue: this.convertToString(change.newValue),
  //         upsertByUserId: adminId,
  //       },
  //     });
  //   }

  //   return this.toResponseDto({
  //     employeeId: employee.id,
  //     bankData: updatedBankData,
  //   });
  // }

  // async findById(id: string): Promise<BankDataResponseDto> {
  //   const bankData = await this.prisma.bankData.findUnique({
  //     where: { id },
  //     include: {
  //       employee: {
  //         select: {
  //           id: true,
  //         },
  //       },
  //     },
  //   });

  //   if (!bankData) {
  //     throw new NotFoundException('Bank data not found');
  //   }

  //   return this.toResponseDto({
  //     employeeId: bankData.employee?.id || 0,
  //     bankData,
  //   });
  // }

  // async findAll({
  //   page = 1,
  //   limit = 10,
  //   employeeId,
  //   status,
  // }: {
  //   page?: number;
  //   limit?: number;
  //   employeeId?: number;
  //   status?: BankDataStatus;
  // } = {}): Promise<{
  //   data: BankDataResponseDto[];
  //   total: number;
  //   page: number;
  //   limit: number;
  //   totalPages: number;
  // }> {
  //   const skip = (page - 1) * limit;

  //   // Construir filtros
  //   const where: Prisma.BankDataWhereInput = {
  //     deletedAt: null, // Apenas registros não deletados
  //   };

  //   if (employeeId) {
  //     where.employeeId = employeeId;
  //   }

  //   if (status) {
  //     where.status = status;
  //   }

  //   // Buscar dados com paginação
  //   const [bankDataList, total] = await Promise.all([
  //     this.prisma.bankData.findMany({
  //       where,
  //       include: {
  //         employee: {
  //           select: {
  //             id: true,
  //           },
  //         },
  //       },
  //       skip,
  //       take: limit,
  //       orderBy: {
  //         createdAt: 'desc',
  //       },
  //     }),
  //     this.prisma.bankData.count({ where }),
  //   ]);

  //   const data = bankDataList.map((bankData) =>
  //     this.toResponseDto({
  //       employeeId: bankData.employee?.id || 0,
  //       bankData,
  //     }),
  //   );

  //   return {
  //     data,
  //     total,
  //     page,
  //     limit,
  //     totalPages: Math.ceil(total / limit),
  //   };
  // }

  // async findBankAccountUpsertData({
  //   employeeId,
  //   page = 1,
  //   limit = 10,
  //   fieldName,
  // }: {
  //   employeeId?: number;
  //   page?: number;
  //   limit?: number;
  //   fieldName?: string;
  // }): Promise<{
  //   data: Array<{
  //     id: string;
  //     bankDataId: string;
  //     fieldName: string;
  //     oldValue: string | null;
  //     newValue: string | null;
  //     upsertByUserId: string | null;
  //     upsertByUserName: string | null;
  //     upsertByEmployeeId: number | null;
  //     upsertByEmployeeName: string | null;
  //     updatedAt: Date;
  //   }>;
  //   total: number;
  //   page: number;
  //   limit: number;
  //   totalPages: number;
  // }> {
  //   const skip = (page - 1) * limit;

  //   // Construir filtros base
  //   const where: Prisma.BankAccountUpsertDataWhereInput = {};

  //   // Se employeeId foi fornecido, filtrar por ele
  //   if (employeeId !== undefined) {
  //     // Verificar se o funcionário existe e possui dados bancários
  //     const employee = await this.prisma.employee.findUnique({
  //       where: { id: employeeId },
  //       include: {
  //         bankData: true,
  //       },
  //     });

  //     if (!employee) {
  //       throw new NotFoundException('Employee not found');
  //     }

  //     if (!employee.bankData) {
  //       throw new NotFoundException('Employee does not have bank data');
  //     }

  //     where.bankDataId = employee.bankData.id;
  //   }

  //   // Filtro por nome do campo
  //   if (fieldName) {
  //     where.fieldName = {
  //       contains: fieldName,
  //       mode: 'insensitive',
  //     };
  //   }

  //   // Buscar dados com paginação
  //   const [upsertDataList, total] = await Promise.all([
  //     this.prisma.bankAccountUpsertData.findMany({
  //       where,
  //       include: {
  //         upsertByUser: {
  //           select: {
  //             email: true,
  //           },
  //         },
  //         upsertByEmployee: {
  //           select: {
  //             name: true,
  //           },
  //         },
  //       },
  //       skip,
  //       take: limit,
  //       orderBy: {
  //         updatedAt: 'desc',
  //       },
  //     }),
  //     this.prisma.bankAccountUpsertData.count({ where }),
  //   ]);

  //   const data = upsertDataList.map((item) => ({
  //     id: item.id,
  //     bankDataId: item.bankDataId,
  //     fieldName: item.fieldName,
  //     oldValue: item.oldValue,
  //     newValue: item.newValue,
  //     upsertByUserId: item.upsertByUserId,
  //     upsertByUserName: item.upsertByUser?.email || null,
  //     upsertByEmployeeId: item.upsertByEmployeeId,
  //     upsertByEmployeeName: item.upsertByEmployee?.name || null,
  //     updatedAt: item.updatedAt,
  //   }));

  //   return {
  //     data,
  //     total,
  //     page,
  //     limit,
  //     totalPages: Math.ceil(total / limit),
  //   };
  // }

  // private convertToString(value: ChangeLogValue): string | null {
  //   if (value === null || value === undefined) {
  //     return null;
  //   }
  //   return String(value);
  // }

  private toResponseDto({
    user,
    bankData,
  }: {
    user: User;
    bankData: BankData;
  }): BankDataResponseDto & UserDataResponseDto {
    return {
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
        employee?: {
          id: user.employee?.id,
          name: user.employee?.name,
          document: user.employee?.document,
          status: user.employee?.status,
          createdAt: user.employee?.createdAt,
          updatedAt: user.employee?.updatedAt,
        },
        

      },
      bankData: {
        id: bankData.id,
        bankName: bankData.bankName,
        bankCode: bankData.bankCode,
        accountType: bankData.accountType,
        agencyNumber: bankData.agencyNumber,
        agencyDigit: bankData.agencyDigit,
        accountNumber: bankData.accountNumber,
        accountDigit: bankData.accountDigit,
        accountHolderName: bankData.accountHolderName,
        accountHolderDocument: bankData.accountHolderDocument,
        pixKey: bankData.pixKey,
        pixKeyType: bankData.pixKeyType,
        isDigitalBank: bankData.isDigitalBank,
        status: bankData.status,
        createdAt: bankData.createdAt,
        updatedAt: bankData.updatedAt,
      },
    };
  }
}
