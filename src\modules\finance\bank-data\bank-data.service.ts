import {
  Injectable,
  BadRequestException,
  NotFoundException,
} from '@nestjs/common';
import { PrismaService } from '@/infrastructure/prisma/prisma.service';
import { CreateBankDataDto } from './dto/create-bank-data.dto';
import { UpdateBankDataDto } from './dto/update-bank-data.dto';
import { BankDataResponseDto } from './dto/bank-data-response.dto';
import { BANK_LIST } from './constants/bank-list.constant';
import { BankData, BankDataStatus } from '@prisma/client';

@Injectable()
export class BankDataService {
  constructor(private readonly prisma: PrismaService) {}

  async AdmCreate({
    adminId,
    employeeId,
    createBankDataDto,
  }: {
    adminId: string;
    employeeId: string;
    createBankDataDto: CreateBankDataDto;
  }): Promise<BankDataResponseDto> {
    const safeParseEmployeeId = Number(employeeId.trim());

    if (isNaN(safeParseEmployeeId)) {
      throw new BadRequestException('Invalid employee id');
    }

    // Verificar se o funcionário existe
    const employee = await this.prisma.employee.findUnique({
      where: { id: safeParseEmployeeId },
      include: {
        bankData: true,
      },
    });

    if (!employee) {
      throw new BadRequestException('Employee not found');
    }

    // Verificar se o funcionário já possui dados bancários
    const existingBankData = employee.bankData;

    if (existingBankData) {
      throw new BadRequestException(
        'Employee already has bank data. Please update the existing data instead.',
      );
    }

    // Validar e ajustar o nome do banco baseado no código
    if (BANK_LIST[createBankDataDto.bankCode]) {
      createBankDataDto.bankName = BANK_LIST[createBankDataDto.bankCode];
    }

    const bankData = await this.prisma.bankData.create({
      data: {
        bankName: createBankDataDto.bankName,
        bankCode: createBankDataDto.bankCode,
        accountType: createBankDataDto.accountType,
        agencyNumber: createBankDataDto.agencyNumber,
        agencyDigit: createBankDataDto.agencyDigit,
        accountNumber: createBankDataDto.accountNumber,
        accountDigit: createBankDataDto.accountDigit,
        accountHolderName: createBankDataDto.accountHolderName,
        accountHolderDocument: createBankDataDto.accountHolderDocument,
        pixKey: createBankDataDto.pixKey,
        pixKeyType: createBankDataDto.pixKeyType,
        isDigitalBank: createBankDataDto.isDigitalBank || false,
        status: BankDataStatus.ACTIVE,
        employeeId: employee.id,
      },
    });

    await this.prisma.bankAccountUpsertData.create({
      data: {
        bankDataId: bankData.id,
        fieldName: 'CREATED',
        upsertByUserId: adminId,
      },
    });

    return this.toResponseDto({ employeeId: employee.id, bankData });
  }

  async AdmUpdate({
    adminId,
    employeeId,
    updateBankDataDto,
  }: {
    adminId: string;
    employeeId: string;
    updateBankDataDto: UpdateBankDataDto;
  }): Promise<BankDataResponseDto> {
    const safeParseEmployeeId = Number(employeeId.trim());

    if (isNaN(safeParseEmployeeId)) {
      throw new BadRequestException('Invalid employee id');
    }

    // Verificar se o funcionário existe e possui dados bancários
    const employee = await this.prisma.employee.findUnique({
      where: { id: safeParseEmployeeId },
      include: {
        bankData: true,
      },
    });

    if (!employee) {
      throw new NotFoundException('Employee not found');
    }

    if (!employee.bankData) {
      throw new NotFoundException('Employee does not have bank data to update');
    }

    // Preparar dados para atualização - apenas campos que foram enviados
    const updateData: Partial<BankData> = {};
    const changedFields: Array<{
      fieldName: string;
      oldValue: any;
      newValue: any;
    }> = [];

    // Verificar cada campo e adicionar apenas se foi enviado
    if (updateBankDataDto.bankName !== undefined) {
      if (updateBankDataDto.bankName !== employee.bankData.bankName) {
        changedFields.push({
          fieldName: 'bankName',
          oldValue: employee.bankData.bankName,
          newValue: updateBankDataDto.bankName,
        });
        updateData.bankName = updateBankDataDto.bankName;
      }
    }

    if (updateBankDataDto.bankCode !== undefined) {
      // Validar e ajustar o nome do banco baseado no código se fornecido
      if (BANK_LIST[updateBankDataDto.bankCode]) {
        updateData.bankName = BANK_LIST[updateBankDataDto.bankCode];
        if (updateData.bankName !== employee.bankData.bankName) {
          changedFields.push({
            fieldName: 'bankName',
            oldValue: employee.bankData.bankName,
            newValue: updateData.bankName,
          });
        }
      }

      if (updateBankDataDto.bankCode !== employee.bankData.bankCode) {
        changedFields.push({
          fieldName: 'bankCode',
          oldValue: employee.bankData.bankCode,
          newValue: updateBankDataDto.bankCode,
        });
        updateData.bankCode = updateBankDataDto.bankCode;
      }
    }

    if (updateBankDataDto.accountType !== undefined) {
      if (updateBankDataDto.accountType !== employee.bankData.accountType) {
        changedFields.push({
          fieldName: 'accountType',
          oldValue: employee.bankData.accountType,
          newValue: updateBankDataDto.accountType,
        });
        updateData.accountType = updateBankDataDto.accountType;
      }
    }

    if (updateBankDataDto.agencyNumber !== undefined) {
      if (updateBankDataDto.agencyNumber !== employee.bankData.agencyNumber) {
        changedFields.push({
          fieldName: 'agencyNumber',
          oldValue: employee.bankData.agencyNumber,
          newValue: updateBankDataDto.agencyNumber,
        });
        updateData.agencyNumber = updateBankDataDto.agencyNumber;
      }
    }

    // Para campos nullable, verificar se foi explicitamente enviado
    if (updateBankDataDto.hasOwnProperty('agencyDigit')) {
      if (updateBankDataDto.agencyDigit !== employee.bankData.agencyDigit) {
        changedFields.push({
          fieldName: 'agencyDigit',
          oldValue: employee.bankData.agencyDigit,
          newValue: updateBankDataDto.agencyDigit,
        });
        updateData.agencyDigit = updateBankDataDto.agencyDigit;
      }
    }

    if (updateBankDataDto.accountNumber !== undefined) {
      if (updateBankDataDto.accountNumber !== employee.bankData.accountNumber) {
        changedFields.push({
          fieldName: 'accountNumber',
          oldValue: employee.bankData.accountNumber,
          newValue: updateBankDataDto.accountNumber,
        });
        updateData.accountNumber = updateBankDataDto.accountNumber;
      }
    }

    if (updateBankDataDto.accountDigit !== undefined) {
      if (updateBankDataDto.accountDigit !== employee.bankData.accountDigit) {
        changedFields.push({
          fieldName: 'accountDigit',
          oldValue: employee.bankData.accountDigit,
          newValue: updateBankDataDto.accountDigit,
        });
        updateData.accountDigit = updateBankDataDto.accountDigit;
      }
    }

    if (updateBankDataDto.accountHolderName !== undefined) {
      if (
        updateBankDataDto.accountHolderName !==
        employee.bankData.accountHolderName
      ) {
        changedFields.push({
          fieldName: 'accountHolderName',
          oldValue: employee.bankData.accountHolderName,
          newValue: updateBankDataDto.accountHolderName,
        });
        updateData.accountHolderName = updateBankDataDto.accountHolderName;
      }
    }

    if (updateBankDataDto.accountHolderDocument !== undefined) {
      if (
        updateBankDataDto.accountHolderDocument !==
        employee.bankData.accountHolderDocument
      ) {
        changedFields.push({
          fieldName: 'accountHolderDocument',
          oldValue: employee.bankData.accountHolderDocument,
          newValue: updateBankDataDto.accountHolderDocument,
        });
        updateData.accountHolderDocument =
          updateBankDataDto.accountHolderDocument;
      }
    }

    // Para campos nullable
    if (updateBankDataDto.hasOwnProperty('pixKey')) {
      if (updateBankDataDto.pixKey !== employee.bankData.pixKey) {
        changedFields.push({
          fieldName: 'pixKey',
          oldValue: employee.bankData.pixKey,
          newValue: updateBankDataDto.pixKey,
        });
        updateData.pixKey = updateBankDataDto.pixKey;
      }
    }

    if (updateBankDataDto.hasOwnProperty('pixKeyType')) {
      if (updateBankDataDto.pixKeyType !== employee.bankData.pixKeyType) {
        changedFields.push({
          fieldName: 'pixKeyType',
          oldValue: employee.bankData.pixKeyType,
          newValue: updateBankDataDto.pixKeyType,
        });
        updateData.pixKeyType = updateBankDataDto.pixKeyType;
      }
    }

    if (updateBankDataDto.hasOwnProperty('isDigitalBank')) {
      if (updateBankDataDto.isDigitalBank !== employee.bankData.isDigitalBank) {
        changedFields.push({
          fieldName: 'isDigitalBank',
          oldValue: employee.bankData.isDigitalBank,
          newValue: updateBankDataDto.isDigitalBank,
        });
        updateData.isDigitalBank = updateBankDataDto.isDigitalBank;
      }
    }

    if (updateBankDataDto.hasOwnProperty('status')) {
      if (updateBankDataDto.status !== employee.bankData.status) {
        changedFields.push({
          fieldName: 'status',
          oldValue: employee.bankData.status,
          newValue: updateBankDataDto.status,
        });
        updateData.status = updateBankDataDto.status;
      }
    }

    // Se não há mudanças, retornar os dados atuais
    if (changedFields.length === 0) {
      return this.toResponseDto({
        employeeId: employee.id,
        bankData: employee.bankData,
      });
    }

    // Atualizar os dados bancários
    const updatedBankData = await this.prisma.bankData.update({
      where: { id: employee.bankData.id },
      data: updateData,
    });

    // Registrar as mudanças no histórico
    for (const change of changedFields) {
      await this.prisma.bankAccountUpsertData.create({
        data: {
          bankDataId: employee.bankData.id,
          fieldName: change.fieldName,
          oldValue: change.oldValue?.toString() || null,
          newValue: change.newValue?.toString() || null,
          upsertByUserId: adminId,
        },
      });
    }

    return this.toResponseDto({
      employeeId: employee.id,
      bankData: updatedBankData,
    });
  }

  private toResponseDto({
    employeeId,
    bankData,
  }: {
    employeeId: number;
    bankData: BankData;
  }): BankDataResponseDto {
    return {
      id: bankData.id,
      bankName: bankData.bankName,
      bankCode: bankData.bankCode,
      accountType: bankData.accountType,
      agencyNumber: bankData.agencyNumber,
      agencyDigit: bankData.agencyDigit,
      accountNumber: bankData.accountNumber,
      accountDigit: bankData.accountDigit,
      accountHolderName: bankData.accountHolderName,
      accountHolderDocument: bankData.accountHolderDocument,
      pixKey: bankData.pixKey,
      pixKeyType: bankData.pixKeyType,
      isDigitalBank: bankData.isDigitalBank,
      status: bankData.status,
      employeeId,
      createdAt: bankData.createdAt,
      updatedAt: bankData.updatedAt,
    };
  }
}
