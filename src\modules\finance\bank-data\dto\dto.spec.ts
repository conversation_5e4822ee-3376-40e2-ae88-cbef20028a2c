import { validate } from 'class-validator';
import { plainToClass } from 'class-transformer';
import { CreateBankDataDto } from './create-bank-data.dto';
import { UpdateBankDataDto } from './update-bank-data.dto';
import { GetBankDataQueryDto } from './get-bank-data-query.dto';
import { GetBankAccountUpsertDataQueryDto } from './get-bank-account-upsert-data-query.dto';
import {
  BankAccountType,
  BankPixKeyType,
  BankDataStatus,
} from '@prisma/client';

describe('BankData DTOs', () => {
  describe('CreateBankDataDto', () => {
    it('should validate a valid CreateBankDataDto', async () => {
      const dto = plainToClass(CreateBankDataDto, {
        bankName: 'Banco do Brasil S.A.',
        bankCode: '001',
        accountType: BankAccountType.CHECKING,
        agencyNumber: '1234',
        agencyDigit: '5',
        accountNumber: '123456',
        accountDigit: '7',
        accountHolderName: '<PERSON>',
        accountHolderDocument: '***********',
        pixKey: '<EMAIL>',
        pixKeyType: BankPixKeyType.EMAIL,
        isDigitalBank: false,
        status: BankDataStatus.ACTIVE,
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should fail validation for missing required fields', async () => {
      const dto = plainToClass(CreateBankDataDto, {});

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);

      const errorMessages = errors
        .map((error) => Object.values(error.constraints || {}))
        .flat();
      expect(errorMessages).toContain('Bank name is required');
      expect(errorMessages).toContain('Bank code is required');
      expect(errorMessages).toContain('Account type is required');
    });

    it('should fail validation for invalid bank code format', async () => {
      const dto = plainToClass(CreateBankDataDto, {
        bankName: 'Banco do Brasil S.A.',
        bankCode: 'invalid-code',
        accountType: BankAccountType.CHECKING,
        agencyNumber: '1234',
        accountNumber: '123456',
        accountDigit: '7',
        accountHolderName: 'John Doe',
        accountHolderDocument: '***********',
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);

      const bankCodeError = errors.find(
        (error) => error.property === 'bankCode',
      );
      expect(bankCodeError).toBeDefined();
    });

    it('should fail validation for invalid document length', async () => {
      const dto = plainToClass(CreateBankDataDto, {
        bankName: 'Banco do Brasil S.A.',
        bankCode: '001',
        accountType: BankAccountType.CHECKING,
        agencyNumber: '1234',
        accountNumber: '123456',
        accountDigit: '7',
        accountHolderName: 'John Doe',
        accountHolderDocument: '123', // Too short
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);

      const documentError = errors.find(
        (error) => error.property === 'accountHolderDocument',
      );
      expect(documentError).toBeDefined();
    });
  });

  describe('UpdateBankDataDto', () => {
    it('should validate a valid UpdateBankDataDto with partial data', async () => {
      const dto = plainToClass(UpdateBankDataDto, {
        bankName: 'Itaú Unibanco S.A.',
        bankCode: '341',
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should validate UpdateBankDataDto with null values', async () => {
      const dto = plainToClass(UpdateBankDataDto, {
        agencyDigit: null,
        pixKey: null,
        pixKeyType: null,
        isDigitalBank: null,
        status: null,
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should validate empty UpdateBankDataDto', async () => {
      const dto = plainToClass(UpdateBankDataDto, {});

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });
  });

  describe('GetBankDataQueryDto', () => {
    it('should validate valid query parameters', async () => {
      const dto = plainToClass(GetBankDataQueryDto, {
        page: '1',
        limit: '10',
        employeeId: '123',
        status: BankDataStatus.ACTIVE,
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
      expect(dto.page).toBe(1);
      expect(dto.limit).toBe(10);
      expect(dto.employeeId).toBe(123);
    });

    it('should use default values when not provided', async () => {
      const dto = plainToClass(GetBankDataQueryDto, {});

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
      expect(dto.page).toBe(1);
      expect(dto.limit).toBe(10);
    });

    it('should fail validation for invalid page number', async () => {
      const dto = plainToClass(GetBankDataQueryDto, {
        page: '0',
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);

      const pageError = errors.find((error) => error.property === 'page');
      expect(pageError).toBeDefined();
    });

    it('should fail validation for limit exceeding maximum', async () => {
      const dto = plainToClass(GetBankDataQueryDto, {
        limit: '101',
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);

      const limitError = errors.find((error) => error.property === 'limit');
      expect(limitError).toBeDefined();
    });
  });

  describe('GetBankAccountUpsertDataQueryDto', () => {
    it('should validate valid query parameters', async () => {
      const dto = plainToClass(GetBankAccountUpsertDataQueryDto, {
        page: '1',
        limit: '10',
        employeeId: '123',
        fieldName: 'bankName',
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
      expect(dto.page).toBe(1);
      expect(dto.limit).toBe(10);
      expect(dto.employeeId).toBe(123);
      expect(dto.fieldName).toBe('bankName');
    });

    it('should validate without optional parameters', async () => {
      const dto = plainToClass(GetBankAccountUpsertDataQueryDto, {});

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
      expect(dto.page).toBe(1);
      expect(dto.limit).toBe(10);
    });

    it('should fail validation for invalid employeeId', async () => {
      const dto = plainToClass(GetBankAccountUpsertDataQueryDto, {
        employeeId: 'invalid',
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);

      const employeeIdError = errors.find(
        (error) => error.property === 'employeeId',
      );
      expect(employeeIdError).toBeDefined();
    });

    it('should fail validation for fieldName exceeding max length', async () => {
      const dto = plainToClass(GetBankAccountUpsertDataQueryDto, {
        fieldName: 'a'.repeat(101), // Exceeds 100 character limit
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);

      const fieldNameError = errors.find(
        (error) => error.property === 'fieldName',
      );
      expect(fieldNameError).toBeDefined();
    });
  });
});
