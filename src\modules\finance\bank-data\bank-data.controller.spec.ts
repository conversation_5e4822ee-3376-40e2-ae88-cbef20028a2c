import { Test, TestingModule } from '@nestjs/testing';
import { BankDataController } from './bank-data.controller';
import { BankDataService } from './bank-data.service';
import { JwtAuthGuard } from '@modules/auth/guards/jwt-auth.guard';
import { RolesGuard } from '@modules/auth/guards/roles.guard';
import {
  BankDataStatus,
  BankAccountType,
  BankPixKeyType,
} from '@prisma/client';

describe('BankDataController', () => {
  let controller: BankDataController;

  const mockBankDataService = {
    AdmCreate: jest.fn(),
    AdmUpdate: jest.fn(),
    findById: jest.fn(),
    findAll: jest.fn(),
    findBankAccountUpsertData: jest.fn(),
  };

  const mockBankDataResponse = {
    id: 'bank-data-uuid',
    bankName: 'Banco do Brasil S.A.',
    bankCode: '001',
    accountType: BankAccountType.CHECKING,
    agencyNumber: '1234',
    agencyDigit: '5',
    accountNumber: '123456',
    accountDigit: '7',
    accountHolderName: '<PERSON>',
    accountHolderDocument: '***********',
    pixKey: '<EMAIL>',
    pixKeyType: BankPixKeyType.EMAIL,
    isDigitalBank: false,
    status: BankDataStatus.ACTIVE,
    employeeId: 1,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [BankDataController],
      providers: [
        {
          provide: BankDataService,
          useValue: mockBankDataService,
        },
      ],
    })
      .overrideGuard(JwtAuthGuard)
      .useValue({ canActivate: jest.fn(() => true) })
      .overrideGuard(RolesGuard)
      .useValue({ canActivate: jest.fn(() => true) })
      .compile();

    controller = module.get<BankDataController>(BankDataController);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('upsert', () => {
    it('should create bank data successfully', async () => {
      const createDto = {
        bankName: 'Banco do Brasil S.A.',
        bankCode: '001',
        accountType: BankAccountType.CHECKING,
        agencyNumber: '1234',
        agencyDigit: '5',
        accountNumber: '123456',
        accountDigit: '7',
        accountHolderName: 'John Doe',
        accountHolderDocument: '***********',
        pixKey: '<EMAIL>',
        pixKeyType: BankPixKeyType.EMAIL,
        isDigitalBank: false,
        status: BankDataStatus.ACTIVE,
      };

      mockBankDataService.AdmCreate.mockResolvedValue(mockBankDataResponse);

      const result = await controller.upsert(
        'employee-uuid',
        createDto,
        'admin-uuid',
      );

      expect(result).toEqual(mockBankDataResponse);
      expect(mockBankDataService.AdmCreate).toHaveBeenCalledWith({
        adminId: 'admin-uuid',
        employeeId: 'employee-uuid',
        createBankDataDto: createDto,
      });
    });
  });

  describe('update', () => {
    it('should update bank data successfully', async () => {
      const updateDto = {
        bankName: 'Itaú Unibanco S.A.',
        bankCode: '341',
      };

      const updatedResponse = {
        ...mockBankDataResponse,
        bankName: 'Itaú Unibanco S.A.',
        bankCode: '341',
      };

      mockBankDataService.AdmUpdate.mockResolvedValue(updatedResponse);

      const result = await controller.update(
        'employee-uuid',
        updateDto,
        'admin-uuid',
      );

      expect(result).toEqual(updatedResponse);
      expect(mockBankDataService.AdmUpdate).toHaveBeenCalledWith({
        adminId: 'admin-uuid',
        employeeId: 'employee-uuid',
        updateBankDataDto: updateDto,
      });
    });
  });

  describe('findById', () => {
    it('should find bank data by id successfully', async () => {
      mockBankDataService.findById.mockResolvedValue(mockBankDataResponse);

      const result = await controller.findById('bank-data-uuid');

      expect(result).toEqual(mockBankDataResponse);
      expect(mockBankDataService.findById).toHaveBeenCalledWith(
        'bank-data-uuid',
      );
    });
  });

  describe('findAll', () => {
    it('should find all bank data with pagination', async () => {
      const mockPaginatedResponse = {
        data: [mockBankDataResponse],
        total: 1,
        page: 1,
        limit: 10,
        totalPages: 1,
      };

      const query = {
        page: 1,
        limit: 10,
        employeeId: undefined,
        status: undefined,
      };

      mockBankDataService.findAll.mockResolvedValue(mockPaginatedResponse);

      const result = await controller.findAll(query);

      expect(result).toEqual(mockPaginatedResponse);
      expect(mockBankDataService.findAll).toHaveBeenCalledWith({
        page: 1,
        limit: 10,
        employeeId: undefined,
        status: undefined,
      });
    });

    it('should find all bank data with filters', async () => {
      const mockPaginatedResponse = {
        data: [mockBankDataResponse],
        total: 1,
        page: 1,
        limit: 10,
        totalPages: 1,
      };

      const query = {
        page: 1,
        limit: 10,
        employeeId: 1,
        status: BankDataStatus.ACTIVE,
      };

      mockBankDataService.findAll.mockResolvedValue(mockPaginatedResponse);

      const result = await controller.findAll(query);

      expect(result).toEqual(mockPaginatedResponse);
      expect(mockBankDataService.findAll).toHaveBeenCalledWith({
        page: 1,
        limit: 10,
        employeeId: 1,
        status: BankDataStatus.ACTIVE,
      });
    });
  });

  describe('findBankAccountUpsertData', () => {
    it('should find upsert data for all employees', async () => {
      const mockUpsertResponse = {
        data: [
          {
            id: 'upsert-uuid',
            bankDataId: 'bank-data-uuid',
            fieldName: 'bankName',
            oldValue: 'Old Bank',
            newValue: 'New Bank',
            upsertByUserId: 'user-uuid',
            upsertByUserName: '<EMAIL>',
            upsertByEmployeeId: null,
            upsertByEmployeeName: null,
            updatedAt: new Date(),
          },
        ],
        total: 1,
        page: 1,
        limit: 10,
        totalPages: 1,
      };

      const query = {
        page: 1,
        limit: 10,
        employeeId: undefined,
        fieldName: undefined,
      };

      mockBankDataService.findBankAccountUpsertData.mockResolvedValue(
        mockUpsertResponse,
      );

      const result = await controller.findBankAccountUpsertData(query);

      expect(result).toEqual(mockUpsertResponse);
      expect(
        mockBankDataService.findBankAccountUpsertData,
      ).toHaveBeenCalledWith({
        employeeId: undefined,
        page: 1,
        limit: 10,
        fieldName: undefined,
      });
    });

    it('should find upsert data for specific employee with field filter', async () => {
      const mockUpsertResponse = {
        data: [
          {
            id: 'upsert-uuid',
            bankDataId: 'bank-data-uuid',
            fieldName: 'bankName',
            oldValue: 'Old Bank',
            newValue: 'New Bank',
            upsertByUserId: 'user-uuid',
            upsertByUserName: '<EMAIL>',
            upsertByEmployeeId: null,
            upsertByEmployeeName: null,
            updatedAt: new Date(),
          },
        ],
        total: 1,
        page: 1,
        limit: 10,
        totalPages: 1,
      };

      const query = {
        page: 1,
        limit: 10,
        employeeId: 1,
        fieldName: 'bankName',
      };

      mockBankDataService.findBankAccountUpsertData.mockResolvedValue(
        mockUpsertResponse,
      );

      const result = await controller.findBankAccountUpsertData(query);

      expect(result).toEqual(mockUpsertResponse);
      expect(
        mockBankDataService.findBankAccountUpsertData,
      ).toHaveBeenCalledWith({
        employeeId: 1,
        page: 1,
        limit: 10,
        fieldName: 'bankName',
      });
    });
  });
});
