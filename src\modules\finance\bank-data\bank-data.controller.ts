/* eslint-disable @typescript-eslint/no-unsafe-assignment */
/* eslint-disable @typescript-eslint/ban-ts-comment */
// @ts-ignore
// @ts-nocheck
import {
  Controller,
  Post,
  // Put,
  // Get,
  Body,
  UseGuards,
  HttpCode,
  HttpStatus,
  Param,
  Request,
  // Query,
} from '@nestjs/common';
import {
  ApiTags,
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiCreatedResponse,
  // ApiOkResponse,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '@modules/auth/guards/jwt-auth.guard';
import { RolesGuard } from '@modules/auth/guards/roles.guard';
import { Roles } from '@modules/auth/decorators/roles.decorator';
import { Role } from '@/core/domain/role.enum';
import { BankDataService } from './bank-data.service';
import { CreateBankDataDto } from './dto/create-bank-data.dto';
// import { UpdateBankDataDto } from './dto/update-bank-data.dto';
import { BankDataResponseDto } from './dto/bank-data-response.dto';
// import { GetBankDataQueryDto } from './dto/get-bank-data-query.dto';
// import { PaginatedBankDataResponseDto } from './dto/paginated-bank-data-response.dto';
// import { GetBankAccountUpsertDataQueryDto } from './dto/get-bank-account-upsert-data-query.dto';
// import { PaginatedBankAccountUpsertDataResponseDto } from './dto/bank-account-upsert-data-list-response.dto';

interface AuthenticatedRequest extends Request {
  user: {
    preferred_username: string;
    email: string;
  };
}

@ApiTags('Bank Data')
@Controller('finance/bank-data/employee')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class BankDataController {
  constructor(private readonly bankDataService: BankDataService) {}
  @Post('/:uuid')
  @Roles(Role.ADMIN, Role.FINANCE_ADMIN, Role.EMPLOYEE)
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'Create bank data for employees' })
  @ApiCreatedResponse({
    description: 'Bank data created successfully',
    type: BankDataResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Employee not found',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Forbidden',
  })
  async create(
    @Param('uuid') uuid: string, // Ok
    @Body() createBankDataDto: CreateBankDataDto,
    @Request() req: AuthenticatedRequest,
  ): Promise<BankDataResponseDto> {
    const requestUserEmail = req?.user?.preferred_username || req?.user?.email;
    return this.bankDataService.AdmCreate({
      requestUserEmail,
      paramId: uuid,
      createBankDataDto,
    });
  }

  // @Put('admin/update/:uuid')
  // @Roles(Role.ADMIN)
  // @HttpCode(HttpStatus.OK)
  // @ApiOperation({ summary: 'Update bank data for employees' })
  // @ApiOkResponse({
  //   description: 'Bank data updated successfully',
  //   type: BankDataResponseDto,
  // })
  // @ApiResponse({
  //   status: HttpStatus.NOT_FOUND,
  //   description: 'Employee not found or employee does not have bank data',
  // })
  // @ApiResponse({
  //   status: HttpStatus.UNAUTHORIZED,
  //   description: 'Unauthorized',
  // })
  // @ApiResponse({
  //   status: HttpStatus.FORBIDDEN,
  //   description: 'Forbidden',
  // })
  // async update(
  //   @Param('uuid') uuid: string,
  //   @Body() updateBankDataDto: UpdateBankDataDto,
  //   @User('id') userId: string,
  // ): Promise<BankDataResponseDto> {
  //   return this.bankDataService.AdmUpdate({
  //     adminId: userId,
  //     employeeId: uuid,
  //     updateBankDataDto,
  //   });
  // }

  // @Get('admin/list/:id')
  // @Roles(Role.ADMIN)
  // @HttpCode(HttpStatus.OK)
  // @ApiOperation({ summary: 'Get bank data by ID' })
  // @ApiOkResponse({
  //   description: 'Bank data retrieved successfully',
  //   type: BankDataResponseDto,
  // })
  // @ApiResponse({
  //   status: HttpStatus.NOT_FOUND,
  //   description: 'Bank data not found',
  // })
  // @ApiResponse({
  //   status: HttpStatus.UNAUTHORIZED,
  //   description: 'Unauthorized',
  // })
  // @ApiResponse({
  //   status: HttpStatus.FORBIDDEN,
  //   description: 'Forbidden',
  // })
  // async findById(@Param('id') id: string): Promise<BankDataResponseDto> {
  //   return this.bankDataService.findById(id);
  // }

  // @Get('admin/list')
  // @Roles(Role.ADMIN)
  // @HttpCode(HttpStatus.OK)
  // @ApiOperation({ summary: 'Get all bank data with pagination' })
  // @ApiOkResponse({
  //   description: 'Bank data list retrieved successfully',
  //   type: PaginatedBankDataResponseDto,
  // })
  // @ApiResponse({
  //   status: HttpStatus.UNAUTHORIZED,
  //   description: 'Unauthorized',
  // })
  // @ApiResponse({
  //   status: HttpStatus.FORBIDDEN,
  //   description: 'Forbidden',
  // })
  // async findAll(
  //   @Query() query: GetBankDataQueryDto,
  // ): Promise<PaginatedBankDataResponseDto> {
  //   return this.bankDataService.findAll({
  //     page: query.page,
  //     limit: query.limit,
  //     employeeId: query.employeeId,
  //     status: query.status,
  //   });
  // }

  // @Get('admin/history')
  // @Roles(Role.ADMIN)
  // @HttpCode(HttpStatus.OK)
  // @ApiOperation({
  //   summary: 'Get bank data change history',
  //   description:
  //     'Get bank data change history. If employeeId is provided, returns history for that employee only. Otherwise, returns history for all employees.',
  // })
  // @ApiOkResponse({
  //   description: 'Bank data change history retrieved successfully',
  //   type: PaginatedBankAccountUpsertDataResponseDto,
  // })
  // @ApiResponse({
  //   status: HttpStatus.NOT_FOUND,
  //   description:
  //     'Employee not found or employee does not have bank data (when employeeId is provided)',
  // })
  // @ApiResponse({
  //   status: HttpStatus.UNAUTHORIZED,
  //   description: 'Unauthorized',
  // })
  // @ApiResponse({
  //   status: HttpStatus.FORBIDDEN,
  //   description: 'Forbidden',
  // })
  // async findBankAccountUpsertData(
  //   @Query() query: GetBankAccountUpsertDataQueryDto,
  // ): Promise<PaginatedBankAccountUpsertDataResponseDto> {
  //   return this.bankDataService.findBankAccountUpsertData({
  //     employeeId: query?.employeeId,
  //     page: query.page,
  //     limit: query.limit,
  //     fieldName: query.fieldName,
  //   });
  // }
}
