import {
  Controller,
  Post,
  Body,
  UseGuards,
  HttpCode,
  HttpStatus,
  Param,
} from '@nestjs/common';
import {
  ApiTags,
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiCreatedResponse,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '@modules/auth/guards/jwt-auth.guard';
import { RolesGuard } from '@modules/auth/guards/roles.guard';
import { Roles } from '@modules/auth/decorators/roles.decorator';
import { Role } from '@/core/domain/role.enum';
import { BankDataService } from './bank-data.service';
import { CreateBankDataDto } from './dto/create-bank-data.dto';
import { BankDataResponseDto } from './dto/bank-data-response.dto';
import { User } from '@/modules/auth/decorators/user.decorator';

@ApiTags('Bank Data')
@Controller('finance/bank-data/employee')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class BankDataController {
  constructor(private readonly bankDataService: BankDataService) {}
  @Post('admin/upsert/:uuid')
  @Roles(Role.ADMIN)
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'Create bank data for employees' })
  @ApiCreatedResponse({
    description: 'Bank data created successfully',
    type: BankDataResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Employee not found',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Forbidden',
  })
  async upsert(
    @Param('uuid') uuid: string,
    @Body() createBankDataDto: CreateBankDataDto,
    @User('id') userId: string,
  ): Promise<BankDataResponseDto> {
    return this.bankDataService.AdmCreate({
      adminId: userId,
      employeeId: uuid,
      createBankDataDto,
    });
  }
}
