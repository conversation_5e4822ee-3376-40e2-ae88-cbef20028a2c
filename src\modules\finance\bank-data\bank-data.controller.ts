/* eslint-disable @typescript-eslint/ban-ts-comment */
// @ts-ignore
// @ts-nocheck
import {
  Controller,
  Post,
  Put,
  Get,
  Body,
  UseGuards,
  HttpCode,
  HttpStatus,
  Param,
  Request,
  Query,
} from '@nestjs/common';
import {
  ApiTags,
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiCreatedResponse,
  ApiOkResponse,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '@modules/auth/guards/jwt-auth.guard';
import { RolesGuard } from '@modules/auth/guards/roles.guard';
import { Roles } from '@modules/auth/decorators/roles.decorator';
import { Role } from '@/core/domain/role.enum';
import { BankDataService } from './bank-data.service';
import { CreateBankDataDto } from './dto/create-bank-data.dto';
import { BankDataResponseDto } from './dto/bank-data-response.dto';
import { UpdateBankDataDto } from './dto/update-bank-data.dto';
import { GetBankDataQueryDto } from './dto/get-bank-data-query.dto';

interface AuthenticatedRequest extends Request {
  user: {
    preferred_username: string;
    email: string;
  };
}

@ApiTags('Bank Data')
@Controller('finance/bank-data/employee')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class BankDataController {
  constructor(private readonly bankDataService: BankDataService) {}

  private parseIntOrDefault(value: unknown, defaultValue: number): number {
    if (typeof value === 'string') {
      const parsed = parseInt(value, 10);
      return isNaN(parsed) ? defaultValue : parsed;
    }
    return typeof value === 'number' ? value : defaultValue;
  }
  @Post('/:uuid')
  @Roles(
    Role.ADMIN,
    Role.FINANCE_ADMIN,
    Role.EMPLOYEE,
    Role.CUSTOMER,
    Role.CUSTOMER_VIEWER,
    Role.SUPPLIER,
    Role.SUPPLIER_VIEWER,
    Role.USER,
  )
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'Create bank data for employees' })
  @ApiCreatedResponse({
    description: 'Bank data created successfully',
    type: BankDataResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Employee not found',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Forbidden',
  })
  async create(
    @Param('uuid') uuid: string, // Ok
    @Body() createBankDataDto: CreateBankDataDto,
    @Request() req: AuthenticatedRequest,
  ): Promise<BankDataResponseDto> {
    const requestUserEmail = req?.user?.preferred_username || req?.user?.email;
    return this.bankDataService.create({
      requestUserEmail,
      paramId: uuid,
      createBankDataDto,
    });
  }

  @Put('/:uuid')
  @Roles(
    Role.ADMIN,
    Role.FINANCE_ADMIN,
    Role.EMPLOYEE,
    Role.CUSTOMER,
    Role.CUSTOMER_VIEWER,
    Role.SUPPLIER,
    Role.SUPPLIER_VIEWER,
    Role.USER,
  )
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Update bank data for employees' })
  @ApiOkResponse({
    description: 'Bank data updated successfully',
    type: BankDataResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Bank data not found',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Forbidden',
  })
  async update(
    @Param('uuid') uuid: string,
    @Body() updateBankDataDto: UpdateBankDataDto,
    @Request() req: AuthenticatedRequest,
  ): Promise<BankDataResponseDto> {
    const requestUserEmail = req?.user?.preferred_username || req?.user?.email;
    const response = await this.bankDataService.update({
      requestUserEmail,
      bankDataParamId: uuid,
      updateBankDataDto,
    });
    return response.bankData;
  }

  @Get()
  @Roles(Role.ADMIN, Role.FINANCE_ADMIN)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'List all bank data with pagination' })
  @ApiOkResponse({
    description: 'Bank data list retrieved successfully',
    type: [BankDataResponseDto],
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Forbidden',
  })
  async findAll(@Query() query: GetBankDataQueryDto) {
    return this.bankDataService.findAll({
      limit: this.parseIntOrDefault(query.limit, 10),
      offset: this.parseIntOrDefault(query.offset, 0),
      entityType: query.entityType,
      bankName: query.bankName,
      bankCode: query.bankCode,
      accountType: query.accountType,
      agencyNumber: query.agencyNumber,
      agencyDigit: query.agencyDigit,
      accountNumber: query.accountNumber,
      accountDigit: query.accountDigit,
      accountHolderName: query.accountHolderName,
      accountHolderDocument: query.accountHolderDocument,
      pixKey: query.pixKey,
      pixKeyType: query.pixKeyType,
      isDigitalBank: query.isDigitalBank,
      createdAt: query.createdAt ? new Date(query.createdAt) : undefined,
      updatedAt: query.updatedAt ? new Date(query.updatedAt) : undefined,
      status: query.status,
      employeeId: query.employeeId,
    });
  }

  @Get('/:id')
  @Roles(
    Role.ADMIN,
    Role.FINANCE_ADMIN,
    Role.EMPLOYEE,
    Role.CUSTOMER,
    Role.CUSTOMER_VIEWER,
    Role.SUPPLIER,
    Role.SUPPLIER_VIEWER,
    Role.USER,
  )
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Get bank data by ID' })
  @ApiOkResponse({
    description: 'Bank data retrieved successfully',
    type: BankDataResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Bank data not found',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Unauthorized',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Forbidden',
  })
  async findById(
    @Param('id') id: string,
    @Request() req: AuthenticatedRequest,
  ): Promise<BankDataResponseDto> {
    const requestUserEmail = req?.user?.preferred_username || req?.user?.email;
    return this.bankDataService.findById({ bankDataId: id, requestUserEmail });
  }
}
