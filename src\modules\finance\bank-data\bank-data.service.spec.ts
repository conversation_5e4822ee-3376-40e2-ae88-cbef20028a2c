import { Test, TestingModule } from '@nestjs/testing';
import { BadRequestException, NotFoundException } from '@nestjs/common';
import { BankDataService } from './bank-data.service';
import { PrismaService } from '@/infrastructure/prisma/prisma.service';
import {
  BankDataStatus,
  BankAccountType,
  BankPixKeyType,
} from '@prisma/client';
import { CreateBankDataDto } from './dto/create-bank-data.dto';

describe('BankDataService', () => {
  let service: BankDataService;

  const mockPrismaService = {
    employee: {
      findUnique: jest.fn(),
    },
    bankData: {
      create: jest.fn(),
      findUnique: jest.fn(),
      findMany: jest.fn(),
      count: jest.fn(),
      update: jest.fn(),
    },
    bankAccountUpsertData: {
      create: jest.fn(),
      findMany: jest.fn(),
      count: jest.fn(),
    },
  };

  const mockEmployee = {
    id: 1,
    uuid: 'employee-uuid',
    name: '<PERSON>',
    email: '<EMAIL>',
    bankData: null,
  };

  const mockBankData = {
    id: 'bank-data-uuid',
    bankName: 'Banco do Brasil S.A.',
    bankCode: '001',
    accountType: BankAccountType.CHECKING,
    agencyNumber: '1234',
    agencyDigit: '5',
    accountNumber: '123456',
    accountDigit: '7',
    accountHolderName: 'John Doe',
    accountHolderDocument: '***********',
    pixKey: '<EMAIL>',
    pixKeyType: BankPixKeyType.EMAIL,
    isDigitalBank: false,
    status: BankDataStatus.ACTIVE,
    employeeId: 1,
    createdAt: new Date(),
    updatedAt: new Date(),
    deletedAt: null,
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        BankDataService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
      ],
    }).compile();

    service = module.get<BankDataService>(BankDataService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('AdmCreate', () => {
    it('should create bank data successfully', async () => {
      const createDto = {
        bankName: 'Banco do Brasil S.A.',
        bankCode: '001',
        accountType: BankAccountType.CHECKING,
        agencyNumber: '1234',
        agencyDigit: '5',
        accountNumber: '123456',
        accountDigit: '7',
        accountHolderName: 'John Doe',
        accountHolderDocument: '***********',
        pixKey: '<EMAIL>',
        pixKeyType: BankPixKeyType.EMAIL,
        isDigitalBank: false,
        status: BankDataStatus.ACTIVE,
      };

      mockPrismaService.employee.findUnique.mockResolvedValue(mockEmployee);
      mockPrismaService.bankData.create.mockResolvedValue(mockBankData);
      mockPrismaService.bankAccountUpsertData.create.mockResolvedValue({});

      const result = await service.AdmCreate({
        adminId: 'admin-uuid',
        employeeId: '1',
        createBankDataDto: createDto,
      });

      expect(result).toBeDefined();
      expect(result.bankName).toBe(createDto.bankName);
      expect(mockPrismaService.employee.findUnique).toHaveBeenCalledWith({
        where: { id: 1 },
        include: { bankData: true },
      });
    });

    it('should throw BadRequestException for invalid employee id', async () => {
      await expect(
        service.AdmCreate({
          adminId: 'admin-uuid',
          employeeId: 'invalid',
          createBankDataDto: {} as CreateBankDataDto,
        }),
      ).rejects.toThrow(BadRequestException);
    });

    it('should throw BadRequestException if employee not found', async () => {
      mockPrismaService.employee.findUnique.mockResolvedValue(null);

      await expect(
        service.AdmCreate({
          adminId: 'admin-uuid',
          employeeId: '1',
          createBankDataDto: {} as CreateBankDataDto,
        }),
      ).rejects.toThrow(BadRequestException);
    });

    it('should throw BadRequestException if employee already has bank data', async () => {
      mockPrismaService.employee.findUnique.mockResolvedValue({
        ...mockEmployee,
        bankData: mockBankData,
      });

      await expect(
        service.AdmCreate({
          adminId: 'admin-uuid',
          employeeId: '1',
          createBankDataDto: {} as CreateBankDataDto,
        }),
      ).rejects.toThrow(BadRequestException);
    });
  });

  describe('AdmUpdate', () => {
    it('should update bank data successfully', async () => {
      const updateDto = {
        bankName: 'Itaú Unibanco S.A.',
        bankCode: '341',
      };

      const existingBankData = { ...mockBankData };
      const updatedBankData = {
        ...mockBankData,
        bankName: 'Itaú Unibanco S.A.',
        bankCode: '341',
      };

      mockPrismaService.employee.findUnique.mockResolvedValue({
        ...mockEmployee,
        bankData: existingBankData,
      });
      mockPrismaService.bankData.update.mockResolvedValue(updatedBankData);
      mockPrismaService.bankAccountUpsertData.create.mockResolvedValue({});

      const result = await service.AdmUpdate({
        adminId: 'admin-uuid',
        employeeId: '1',
        updateBankDataDto: updateDto,
      });

      expect(result).toBeDefined();
      expect(result.bankName).toBe('Itaú Unibanco S.A.');
      expect(result.bankCode).toBe('341');
    });

    it('should return current data if no changes detected', async () => {
      const updateDto = {
        bankName: 'Banco do Brasil S.A.', // Same as current
      };

      mockPrismaService.employee.findUnique.mockResolvedValue({
        ...mockEmployee,
        bankData: mockBankData,
      });

      const result = await service.AdmUpdate({
        adminId: 'admin-uuid',
        employeeId: '1',
        updateBankDataDto: updateDto,
      });

      expect(result).toBeDefined();
      expect(mockPrismaService.bankData.update).not.toHaveBeenCalled();
    });

    it('should throw NotFoundException if employee not found', async () => {
      mockPrismaService.employee.findUnique.mockResolvedValue(null);

      await expect(
        service.AdmUpdate({
          adminId: 'admin-uuid',
          employeeId: '1',
          updateBankDataDto: {},
        }),
      ).rejects.toThrow(NotFoundException);
    });

    it('should throw NotFoundException if employee has no bank data', async () => {
      mockPrismaService.employee.findUnique.mockResolvedValue(mockEmployee);

      await expect(
        service.AdmUpdate({
          adminId: 'admin-uuid',
          employeeId: '1',
          updateBankDataDto: {},
        }),
      ).rejects.toThrow(NotFoundException);
    });

    it('should handle nullable fields correctly', async () => {
      const updateDto = {
        agencyDigit: null,
        pixKey: null,
      };

      const existingBankData = {
        ...mockBankData,
        agencyDigit: '5',
        pixKey: '<EMAIL>',
      };

      mockPrismaService.employee.findUnique.mockResolvedValue({
        ...mockEmployee,
        bankData: existingBankData,
      });
      mockPrismaService.bankData.update.mockResolvedValue({
        ...existingBankData,
        agencyDigit: null,
        pixKey: null,
      });
      mockPrismaService.bankAccountUpsertData.create.mockResolvedValue({});

      const result = await service.AdmUpdate({
        adminId: 'admin-uuid',
        employeeId: '1',
        updateBankDataDto: updateDto,
      });

      expect(result).toBeDefined();
      expect(
        mockPrismaService.bankAccountUpsertData.create,
      ).toHaveBeenCalledTimes(2);
    });
  });

  describe('findById', () => {
    it('should find bank data by id successfully', async () => {
      mockPrismaService.bankData.findUnique.mockResolvedValue({
        ...mockBankData,
        employee: { id: 1 },
      });

      const result = await service.findById('bank-data-uuid');

      expect(result).toBeDefined();
      expect(result.id).toBe('bank-data-uuid');
      expect(mockPrismaService.bankData.findUnique).toHaveBeenCalledWith({
        where: { id: 'bank-data-uuid' },
        include: {
          employee: {
            select: {
              id: true,
            },
          },
        },
      });
    });

    it('should throw NotFoundException if bank data not found', async () => {
      mockPrismaService.bankData.findUnique.mockResolvedValue(null);

      await expect(service.findById('non-existent-id')).rejects.toThrow(
        NotFoundException,
      );
    });
  });

  describe('findAll', () => {
    it('should find all bank data with pagination', async () => {
      const mockBankDataList = [
        { ...mockBankData, employee: { id: 1 } },
        { ...mockBankData, id: 'bank-data-uuid-2', employee: { id: 2 } },
      ];

      mockPrismaService.bankData.findMany.mockResolvedValue(mockBankDataList);
      mockPrismaService.bankData.count.mockResolvedValue(2);

      const result = await service.findAll({
        page: 1,
        limit: 10,
      });

      expect(result).toBeDefined();
      expect(result.data).toHaveLength(2);
      expect(result.total).toBe(2);
      expect(result.page).toBe(1);
      expect(result.limit).toBe(10);
      expect(result.totalPages).toBe(1);
    });

    it('should filter by employeeId when provided', async () => {
      mockPrismaService.bankData.findMany.mockResolvedValue([]);
      mockPrismaService.bankData.count.mockResolvedValue(0);

      await service.findAll({
        page: 1,
        limit: 10,
        employeeId: 1,
      });

      expect(mockPrismaService.bankData.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            employeeId: 1,
          }) as { employeeId: number },
        }),
      );
    });
  });

  describe('findBankAccountUpsertData', () => {
    it('should find upsert data for all employees when employeeId not provided', async () => {
      const mockUpsertData = [
        {
          id: 'upsert-uuid',
          bankDataId: 'bank-data-uuid',
          fieldName: 'bankName',
          oldValue: 'Old Bank',
          newValue: 'New Bank',
          upsertByUserId: 'user-uuid',
          upsertByEmployeeId: null,
          updatedAt: new Date(),
          upsertByUser: { email: '<EMAIL>' },
          upsertByEmployee: null,
        },
      ];

      mockPrismaService.bankAccountUpsertData.findMany.mockResolvedValue(
        mockUpsertData,
      );
      mockPrismaService.bankAccountUpsertData.count.mockResolvedValue(1);

      const result = await service.findBankAccountUpsertData({
        page: 1,
        limit: 10,
      });

      expect(result).toBeDefined();
      expect(result.data).toHaveLength(1);
      expect(result.data[0].fieldName).toBe('bankName');
    });

    it('should find upsert data for specific employee when employeeId provided', async () => {
      mockPrismaService.employee.findUnique.mockResolvedValue({
        ...mockEmployee,
        bankData: mockBankData,
      });
      mockPrismaService.bankAccountUpsertData.findMany.mockResolvedValue([]);
      mockPrismaService.bankAccountUpsertData.count.mockResolvedValue(0);

      await service.findBankAccountUpsertData({
        employeeId: 1,
        page: 1,
        limit: 10,
      });

      expect(mockPrismaService.employee.findUnique).toHaveBeenCalledWith({
        where: { id: 1 },
        include: { bankData: true },
      });
    });

    it('should throw NotFoundException if employee not found', async () => {
      mockPrismaService.employee.findUnique.mockResolvedValue(null);

      await expect(
        service.findBankAccountUpsertData({
          employeeId: 1,
          page: 1,
          limit: 10,
        }),
      ).rejects.toThrow(NotFoundException);
    });

    it('should throw NotFoundException if employee has no bank data', async () => {
      mockPrismaService.employee.findUnique.mockResolvedValue(mockEmployee);

      await expect(
        service.findBankAccountUpsertData({
          employeeId: 1,
          page: 1,
          limit: 10,
        }),
      ).rejects.toThrow(NotFoundException);
    });
  });
});
