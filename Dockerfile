FROM node:20-alpine AS builder

# Set the working directory
WORKDIR /app

# Copia arquivos de dependência
COPY package*.json ./

# Instala dependências
RUN npm install -g npm && npm install

# Copia código fonte
COPY . .

# Gera o cliente Prisma e compila o código
RUN npx prisma generate

# Stage development: Prepare the runtime image
FROM node:22-alpine AS development

WORKDIR /app

# Copiar package.json e instalar dependências
COPY package*.json ./
RUN npm install -g npm && npm install && npm install -D ts-node-dev

# Copiar o restante do código (mas isso será sobrescrito com volumes no compose)
COPY . .

# Start em modo dev com Hot Reload
CMD ["npm", "run", "start:dev"]

# Stage de produção
FROM node:18-alpine AS production

WORKDIR /app

# Define argumentos e variáveis de ambiente
ARG NODE_ENV=production
ENV NODE_ENV=${NODE_ENV}

# Copia apenas o necessário para produção
COPY --from=builder /app/package*.json ./
COPY --from=builder /app/prisma ./prisma

COPY --from=builder /app /app


# Build the application
RUN npm run build

# Define o ponto de entrada
CMD ["sh", "-c", "npx prisma migrate deploy && npm run start:prod"] 